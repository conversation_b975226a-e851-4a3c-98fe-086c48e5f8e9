<?php
/**
 * Test file for social media links
 * This file is for testing purposes only and should be removed after testing
 */

// Include WordPress
require_once('../../../wp-config.php');

// Include the main plugin file to get the function
require_once('rid-cod-plugin.php');

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار روابط وسائل التواصل الاجتماعي</title>
    <link rel="stylesheet" href="<?php echo plugin_dir_url(__FILE__); ?>assets/css/rid-cod.css">
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار روابط وسائل التواصل الاجتماعي</h1>
        <p>هذا اختبار لعرض روابط وسائل التواصل الاجتماعي كما ستظهر في الإضافة:</p>
        
        <?php
        if (function_exists('rid_cod_display_social_media_links')) {
            rid_cod_display_social_media_links();
        } else {
            echo '<p style="color: red;">خطأ: لم يتم العثور على دالة عرض روابط وسائل التواصل الاجتماعي</p>';
        }
        ?>
        
        <h2>معلومات الاختبار:</h2>
        <ul>
            <li>يوتيوب: https://www.youtube.com/@ridcod_ecom</li>
            <li>فيسبوك: https://www.facebook.com/ridcodecom</li>
            <li>إنستغرام: https://www.instagram.com/ridcod_ecom/</li>
            <li>تليجرام: يحتاج إلى تحديث الرابط</li>
        </ul>
        
        <p><strong>ملاحظة:</strong> يرجى حذف هذا الملف بعد الانتهاء من الاختبار.</p>
    </div>
</body>
</html>
